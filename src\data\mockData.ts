
import { Book, Customer, Rental, DashboardStats } from '../types';

export const mockBooks: Book[] = [
  {
    id: '1',
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    isbn: '978-0-7432-7356-5',
    category: 'Fiction',
    publisher: '<PERSON><PERSON><PERSON>',
    publishYear: 1925,
    stock: 5,
    rentPrice: 15000,
    description: 'A classic American novel set in the Jazz Age',
    imageUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=400&fit=crop',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-15T08:00:00Z'
  },
  {
    id: '2',
    title: 'To Kill a Mockingbird',
    author: '<PERSON>',
    isbn: '978-0-06-112008-4',
    category: 'Fiction',
    publisher: 'J.<PERSON>. <PERSON>cott & Co.',
    publishYear: 1960,
    stock: 3,
    rentPrice: 12000,
    description: 'A gripping tale of racial injustice and childhood innocence',
    imageUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop',
    createdAt: '2024-01-16T09:00:00Z',
    updatedAt: '2024-01-16T09:00:00Z'
  },
  {
    id: '3',
    title: '1984',
    author: 'George Orwell',
    isbn: '978-0-452-28423-4',
    category: 'Dystopian Fiction',
    publisher: 'Secker & Warburg',
    publishYear: 1949,
    stock: 7,
    rentPrice: 18000,
    description: 'A dystopian social science fiction novel',
    imageUrl: 'https://images.unsplash.com/photo-1495640388908-05fa85288e61?w=300&h=400&fit=crop',
    createdAt: '2024-01-17T10:00:00Z',
    updatedAt: '2024-01-17T10:00:00Z'
  }
];

export const mockCustomers: Customer[] = [
  {
    id: '1',
    name: 'Ahmad Rizki',
    email: '<EMAIL>',
    phone: '081234567890',
    address: 'Jl. Sudirman No. 123, Jakarta',
    membershipType: 'gold',
    joinDate: '2023-06-15T08:00:00Z',
    totalRentals: 25,
    status: 'active'
  },
  {
    id: '2',
    name: 'Siti Nurhaliza',
    email: '<EMAIL>',
    phone: '082345678901',
    address: 'Jl. Thamrin No. 456, Jakarta',
    membershipType: 'silver',
    joinDate: '2023-08-20T09:00:00Z',
    totalRentals: 12,
    status: 'active'
  },
  {
    id: '3',
    name: 'Budi Santoso',
    email: '<EMAIL>',
    phone: '083456789012',
    address: 'Jl. Gatot Subroto No. 789, Jakarta',
    membershipType: 'bronze',
    joinDate: '2023-11-10T10:00:00Z',
    totalRentals: 4,
    status: 'active'
  }
];

export const mockRentals: Rental[] = [
  {
    id: '1',
    bookId: '1',
    customerId: '1',
    startDate: '2024-06-01T08:00:00Z',
    endDate: '2024-06-15T08:00:00Z',
    status: 'active',
    totalPrice: 15000,
    notes: 'Peminjaman reguler'
  },
  {
    id: '2',
    bookId: '2',
    customerId: '2',
    startDate: '2024-05-20T09:00:00Z',
    endDate: '2024-06-03T09:00:00Z',
    returnDate: '2024-06-02T14:30:00Z',
    status: 'returned',
    totalPrice: 12000
  },
  {
    id: '3',
    bookId: '3',
    customerId: '3',
    startDate: '2024-05-25T10:00:00Z',
    endDate: '2024-06-08T10:00:00Z',
    status: 'overdue',
    totalPrice: 18000,
    lateFee: 5000,
    notes: 'Telat 3 hari'
  }
];

export const mockDashboardStats: DashboardStats = {
  totalBooks: mockBooks.length,
  totalCustomers: mockCustomers.length,
  activeRentals: mockRentals.filter(r => r.status === 'active').length,
  totalRevenue: mockRentals.reduce((sum, rental) => sum + rental.totalPrice + (rental.lateFee || 0), 0),
  overdueRentals: mockRentals.filter(r => r.status === 'overdue').length,
  availableBooks: mockBooks.reduce((sum, book) => sum + book.stock, 0)
};
