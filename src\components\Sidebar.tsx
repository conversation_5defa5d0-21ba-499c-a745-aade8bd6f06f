
import { useState } from 'react';
import { Book, Users, FileText, BarChart3, Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar = ({ activeTab, onTabChange }: SidebarProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'books', label: 'Manajemen Buku', icon: Book },
    { id: 'customers', label: 'Manajemen Customer', icon: Users },
    { id: 'rentals', label: 'Manajemen Rental', icon: FileText },
  ];

  return (
    <div className={cn(
      "bg-gradient-to-b from-slate-900 to-slate-800 text-white transition-all duration-300 flex flex-col h-screen",
      isCollapsed ? "w-16" : "w-64"
    )}>
      <div className="p-4 border-b border-slate-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                LSP Rental
              </h2>
              <p className="text-slate-300 text-sm">Book Rental System</p>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors"
          >
            {isCollapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
          </button>
        </div>
      </div>

      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.id}>
                <button
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200 text-left",
                    activeTab === item.id
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 shadow-lg"
                      : "hover:bg-slate-700 text-slate-300"
                  )}
                >
                  <Icon className="w-5 h-5 flex-shrink-0" />
                  {!isCollapsed && (
                    <span className="font-medium">{item.label}</span>
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {!isCollapsed && (
        <div className="p-4 border-t border-slate-700">
          <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-3">
            <p className="text-sm font-medium text-blue-300">LSP JWP</p>
            <p className="text-xs text-slate-300">Sistem Rental Buku v1.0</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
