
import { Book, Users, FileText, TrendingUp, Clock, CheckCircle } from 'lucide-react';
import { mockDashboardStats, mockRentals, mockBooks, mockCustomers } from '../data/mockData';

const Dashboard = () => {
  const stats = mockDashboardStats;
  
  const statCards = [
    {
      title: 'Total Buku',
      value: stats.totalBooks,
      icon: Book,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      title: 'Total Customer',
      value: stats.totalCustomers,
      icon: Users,
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    {
      title: 'Rental Aktif',
      value: stats.activeRentals,
      icon: FileText,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600'
    },
    {
      title: 'Total Revenue',
      value: `Rp ${stats.totalRevenue.toLocaleString('id-ID')}`,
      icon: TrendingUp,
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600'
    }
  ];

  const recentRentals = mockRentals.slice(0, 5);
  const popularBooks = mockBooks.slice(0, 3);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Selamat datang di sistem rental buku LSP JWP</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Hari ini</p>
          <p className="text-lg font-semibold text-gray-900">
            {new Date().toLocaleDateString('id-ID', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.textColor}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Rentals */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rental Terbaru</h3>
          <div className="space-y-4">
            {recentRentals.map((rental) => {
              const book = mockBooks.find(b => b.id === rental.bookId);
              const customer = mockCustomers.find(c => c.id === rental.customerId);
              
              return (
                <div key={rental.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      rental.status === 'active' ? 'bg-blue-100 text-blue-600' :
                      rental.status === 'returned' ? 'bg-green-100 text-green-600' :
                      'bg-red-100 text-red-600'
                    }`}>
                      {rental.status === 'active' ? <Clock className="w-4 h-4" /> :
                       rental.status === 'returned' ? <CheckCircle className="w-4 h-4" /> :
                       <Clock className="w-4 h-4" />}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{book?.title}</p>
                      <p className="text-sm text-gray-600">{customer?.name}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">Rp {rental.totalPrice.toLocaleString('id-ID')}</p>
                    <p className={`text-xs px-2 py-1 rounded-full ${
                      rental.status === 'active' ? 'bg-blue-100 text-blue-700' :
                      rental.status === 'returned' ? 'bg-green-100 text-green-700' :
                      'bg-red-100 text-red-700'
                    }`}>
                      {rental.status === 'active' ? 'Aktif' :
                       rental.status === 'returned' ? 'Dikembalikan' :
                       'Terlambat'}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Popular Books */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Buku Populer</h3>
          <div className="space-y-4">
            {popularBooks.map((book) => (
              <div key={book.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <img 
                  src={book.imageUrl} 
                  alt={book.title}
                  className="w-16 h-20 object-cover rounded-lg shadow-sm"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{book.title}</h4>
                  <p className="text-sm text-gray-600">{book.author}</p>
                  <p className="text-sm text-gray-500">{book.category}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">Stok: {book.stock}</p>
                  <p className="text-sm text-gray-600">Rp {book.rentPrice.toLocaleString('id-ID')}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
