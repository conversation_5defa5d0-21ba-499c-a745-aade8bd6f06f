
import { useState } from 'react';
import { Plus, Edit, Trash2, Search, Calendar, Clock, CheckCircle } from 'lucide-react';
import { Rental } from '../types';
import { mockRentals, mockBooks, mockCustomers } from '../data/mockData';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const RentalManager = () => {
  const [rentals, setRentals] = useState<Rental[]>(mockRentals);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingRental, setEditingRental] = useState<Rental | null>(null);

  const statusTypes = ['active', 'returned', 'overdue'];
  
  const filteredRentals = rentals.filter(rental => {
    const book = mockBooks.find(b => b.id === rental.bookId);
    const customer = mockCustomers.find(c => c.id === rental.customerId);
    
    const matchesSearch = book?.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rental.id.includes(searchTerm);
    const matchesStatus = !selectedStatus || rental.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const handleDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus rental ini?')) {
      setRentals(rentals.filter(rental => rental.id !== id));
    }
  };

  const handleEdit = (rental: Rental) => {
    setEditingRental(rental);
    setShowModal(true);
  };

  const handleAdd = () => {
    setEditingRental(null);
    setShowModal(true);
  };

  const handleReturn = (id: string) => {
    const returnDate = new Date().toISOString();
    setRentals(rentals.map(rental => 
      rental.id === id 
        ? { ...rental, status: 'returned' as const, returnDate }
        : rental
    ));
  };

  const handleSave = (rentalData: Partial<Rental>) => {
    if (editingRental) {
      setRentals(rentals.map(rental => 
        rental.id === editingRental.id 
          ? { ...rental, ...rentalData }
          : rental
      ));
    } else {
      const newRental: Rental = {
        id: Date.now().toString(),
        bookId: rentalData.bookId || '',
        customerId: rentalData.customerId || '',
        startDate: rentalData.startDate || new Date().toISOString(),
        endDate: rentalData.endDate || new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        totalPrice: rentalData.totalPrice || 0,
        notes: rentalData.notes || ''
      };
      setRentals([...rentals, newRental]);
    }
    setShowModal(false);
    setEditingRental(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'returned': return 'bg-green-100 text-green-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Clock className="w-4 h-4" />;
      case 'returned': return <CheckCircle className="w-4 h-4" />;
      case 'overdue': return <Clock className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manajemen Rental</h1>
          <p className="text-gray-600 mt-1">Kelola transaksi rental buku</p>
        </div>
        <Button onClick={handleAdd} className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
          <Plus className="w-4 h-4 mr-2" />
          Tambah Rental
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari rental berdasarkan buku, customer, atau ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Semua Status</option>
                <option value="active">Aktif</option>
                <option value="returned">Dikembalikan</option>
                <option value="overdue">Terlambat</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rentals List */}
      <div className="space-y-4">
        {filteredRentals.map((rental) => {
          const book = mockBooks.find(b => b.id === rental.bookId);
          const customer = mockCustomers.find(c => c.id === rental.customerId);
          
          return (
            <Card key={rental.id} className="hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <img
                      src={book?.imageUrl}
                      alt={book?.title}
                      className="w-16 h-20 object-cover rounded-lg shadow-sm"
                    />
                    <div className="space-y-1">
                      <h3 className="font-semibold text-gray-900">{book?.title}</h3>
                      <p className="text-sm text-gray-600">{book?.author}</p>
                      <p className="text-sm text-gray-500">Customer: {customer?.name}</p>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(rental.status)}
                        <Badge className={getStatusColor(rental.status)}>
                          {rental.status === 'active' ? 'Aktif' :
                           rental.status === 'returned' ? 'Dikembalikan' :
                           'Terlambat'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right space-y-2">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">
                        Mulai: {new Date(rental.startDate).toLocaleDateString('id-ID')}
                      </p>
                      <p className="text-sm text-gray-500">
                        Berakhir: {new Date(rental.endDate).toLocaleDateString('id-ID')}
                      </p>
                      {rental.returnDate && (
                        <p className="text-sm text-green-600">
                          Dikembalikan: {new Date(rental.returnDate).toLocaleDateString('id-ID')}
                        </p>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="font-semibold text-gray-900">
                        Total: Rp {rental.totalPrice.toLocaleString('id-ID')}
                      </p>
                      {rental.lateFee && (
                        <p className="text-sm text-red-600">
                          Denda: Rp {rental.lateFee.toLocaleString('id-ID')}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {rental.status === 'active' && (
                      <Button
                        size="sm"
                        onClick={() => handleReturn(rental.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Kembalikan
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(rental)}
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(rental.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                {rental.notes && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Catatan:</span> {rental.notes}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Modal for Add/Edit Rental */}
      {showModal && (
        <RentalModal
          rental={editingRental}
          onSave={handleSave}
          onClose={() => {
            setShowModal(false);
            setEditingRental(null);
          }}
        />
      )}
    </div>
  );
};

interface RentalModalProps {
  rental: Rental | null;
  onSave: (rental: Partial<Rental>) => void;
  onClose: () => void;
}

const RentalModal = ({ rental, onSave, onClose }: RentalModalProps) => {
  const [formData, setFormData] = useState({
    bookId: rental?.bookId || '',
    customerId: rental?.customerId || '',
    startDate: rental?.startDate ? rental.startDate.split('T')[0] : new Date().toISOString().split('T')[0],
    endDate: rental?.endDate ? rental.endDate.split('T')[0] : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    totalPrice: rental?.totalPrice || 0,
    lateFee: rental?.lateFee || 0,
    notes: rental?.notes || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const selectedBook = mockBooks.find(b => b.id === formData.bookId);
    const totalPrice = formData.totalPrice || (selectedBook?.rentPrice || 0);
    
    onSave({
      ...formData,
      startDate: new Date(formData.startDate).toISOString(),
      endDate: new Date(formData.endDate).toISOString(),
      totalPrice
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {rental ? 'Edit Rental' : 'Tambah Rental Baru'}
          </h2>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Buku</label>
            <select
              value={formData.bookId}
              onChange={(e) => {
                const selectedBook = mockBooks.find(b => b.id === e.target.value);
                setFormData({
                  ...formData, 
                  bookId: e.target.value,
                  totalPrice: selectedBook?.rentPrice || 0
                });
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Pilih Buku</option>
              {mockBooks.filter(book => book.stock > 0).map(book => (
                <option key={book.id} value={book.id}>
                  {book.title} - Rp {book.rentPrice.toLocaleString('id-ID')}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
            <select
              value={formData.customerId}
              onChange={(e) => setFormData({...formData, customerId: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Pilih Customer</option>
              {mockCustomers.filter(customer => customer.status === 'active').map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name} ({customer.membershipType.toUpperCase()})
                </option>
              ))}
            </select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tanggal Mulai</label>
              <Input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tanggal Berakhir</label>
              <Input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Harga Total (Rp)</label>
              <Input
                type="number"
                value={formData.totalPrice}
                onChange={(e) => setFormData({...formData, totalPrice: parseInt(e.target.value)})}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Denda (Rp)</label>
              <Input
                type="number"
                value={formData.lateFee}
                onChange={(e) => setFormData({...formData, lateFee: parseInt(e.target.value)})}
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Catatan</label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Catatan tambahan (opsional)"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              {rental ? 'Update' : 'Simpan'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RentalManager;
